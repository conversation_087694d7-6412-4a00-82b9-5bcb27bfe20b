import Admin from '../models/Admin'
import bcrypt from 'bcrypt'
import jwt from 'jsonwebtoken'
import fs from 'fs'
import path from 'path'
import { Op } from 'sequelize'
import { parsePaginationParams, formatPaginationResponse } from '../tool/Common'

const privateKey = fs.readFileSync(path.join(__dirname, '../../publicKey.pub'))

export default class AdminController {
  /**
   * @swagger
   * /admin/login:
   *   post:
   *     tags:
   *       - 管理员模块
   *     summary: 管理员登录
   *     description: 管理员用户名密码登录
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - username
   *               - password
   *             properties:
   *               username:
   *                 type: string
   *                 description: 用户名
   *                 example: "admin"
   *               password:
   *                 type: string
   *                 description: 密码
   *                 example: "123456"
   *     responses:
   *       200:
   *         description: 登录成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "登录成功"
   *                 data:
   *                   type: object
   *                   properties:
   *                     token:
   *                       type: string
   *                       description: JWT token
   *                     admin:
   *                       type: object
   *                       properties:
   *                         id:
   *                           type: integer
   *                         username:
   *                           type: string
   *                         real_name:
   *                           type: string
   *                         role:
   *                           type: string
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 用户名或密码错误
   */
  static async login(ctx) {
    const { username, password } = ctx.request.body

    if (!username || !password) {
      ctx.body = {
        code: 400,
        message: '用户名和密码不能为空'
      }
      return
    }

    try {
      const admin = await Admin.findOne({ 
        where: { 
          username,
          status: 'active'
        } 
      })

      if (!admin) {
        ctx.body = {
          code: 401,
          message: '用户名或密码错误'
        }
        return
      }

      // 验证密码
      const isValidPassword = await bcrypt.compare(password, admin.password)
      if (!isValidPassword) {
        ctx.body = {
          code: 401,
          message: '用户名或密码错误'
        }
        return
      }

      // 更新最后登录时间
      await admin.update({ last_login_at: new Date() })

      // 生成JWT token
      const token = jwt.sign(
        {
          id: admin.id,
          username: admin.username,
          role: admin.role,
          isAdmin: true
        },
        privateKey,
        { expiresIn: '24h' }
      )

      ctx.body = {
        code: 200,
        message: '登录成功',
        data: {
          token,
          admin: {
            id: admin.id,
            username: admin.username,
            real_name: admin.real_name,
            email: admin.email,
            role: admin.role
          }
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '登录失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/profile:
   *   get:
   *     tags:
   *       - 管理员模块
   *     summary: 获取管理员信息
   *     description: 获取当前登录管理员的基本信息
   *     security:
   *       - AdminBearer: []
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getProfile(ctx) {
    const adminId = ctx.state.admin.id

    try {
      const admin = await Admin.findByPk(adminId, {
        attributes: { exclude: ['password'] }
      })

      ctx.body = {
        code: 200,
        data: admin
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取管理员信息失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/admins:
   *   get:
   *     tags:
   *       - 管理员模块
   *     summary: 获取管理员列表
   *     description: 获取管理员列表（仅超级管理员可访问）
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: 搜索关键词
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getAdminList(ctx) {
    const { search } = ctx.query

    try {
      const { pageNum, pageSize, offset } = parsePaginationParams(ctx.query)
      const whereCondition = {}

      if (search) {
        whereCondition[Op.or] = [
          { username: { [Op.like]: `%${search}%` } },
          { real_name: { [Op.like]: `%${search}%` } },
          { email: { [Op.like]: `%${search}%` } }
        ]
      }

      const admins = await Admin.findAndCountAll({
        where: whereCondition,
        attributes: { exclude: ['password'] },
        limit: pageSize,
        offset: offset,
        order: [['created_at', 'DESC']]
      })

      ctx.body = {
        code: 200,
        data: formatPaginationResponse(admins.rows, admins.count, pageNum, pageSize)
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取管理员列表失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/admins:
   *   post:
   *     tags:
   *       - 管理员模块
   *     summary: 创建管理员
   *     description: 创建新的管理员账户（仅超级管理员可访问）
   *     security:
   *       - AdminBearer: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - username
   *               - password
   *               - role
   *             properties:
   *               username:
   *                 type: string
   *                 description: 用户名
   *               password:
   *                 type: string
   *                 description: 密码
   *               real_name:
   *                 type: string
   *                 description: 真实姓名
   *               email:
   *                 type: string
   *                 description: 邮箱
   *               phone:
   *                 type: string
   *                 description: 手机号
   *               role:
   *                 type: string
   *                 enum: [admin, editor]
   *                 description: 角色
   *     responses:
   *       200:
   *         description: 创建成功
   */
  static async createAdmin(ctx) {
    const { username, password, real_name, email, phone, role } = ctx.request.body

    if (!username || !password || !role) {
      ctx.body = {
        code: 400,
        message: '用户名、密码和角色不能为空'
      }
      return
    }

    if (!['admin', 'editor'].includes(role)) {
      ctx.body = {
        code: 400,
        message: '角色只能是admin或editor'
      }
      return
    }

    try {
      // 检查用户名是否已存在
      const existingAdmin = await Admin.findOne({ where: { username } })
      if (existingAdmin) {
        ctx.body = {
          code: 400,
          message: '用户名已存在'
        }
        return
      }

      // 加密密码
      const hashedPassword = await bcrypt.hash(password, 10)

      const admin = await Admin.create({
        username,
        password: hashedPassword,
        real_name,
        email,
        phone,
        role
      })

      ctx.body = {
        code: 200,
        message: '管理员创建成功',
        data: {
          id: admin.id,
          username: admin.username,
          real_name: admin.real_name,
          email: admin.email,
          phone: admin.phone,
          role: admin.role,
          status: admin.status
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '创建管理员失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/admins/{id}:
   *   put:
   *     tags:
   *       - 管理员模块
   *     summary: 更新管理员信息
   *     description: 更新管理员信息（仅超级管理员可访问）
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 管理员ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               real_name:
   *                 type: string
   *               email:
   *                 type: string
   *               phone:
   *                 type: string
   *               role:
   *                 type: string
   *                 enum: [admin, editor]
   *               status:
   *                 type: string
   *                 enum: [active, inactive]
   *     responses:
   *       200:
   *         description: 更新成功
   */
  static async updateAdmin(ctx) {
    const { id } = ctx.params
    const { real_name, email, phone, role, status } = ctx.request.body

    try {
      const admin = await Admin.findByPk(id)
      if (!admin) {
        ctx.body = {
          code: 404,
          message: '管理员不存在'
        }
        return
      }

      // 不能修改超级管理员
      if (admin.role === 'super_admin') {
        ctx.body = {
          code: 403,
          message: '不能修改超级管理员'
        }
        return
      }

      const updateData = {}
      if (real_name !== undefined) updateData.real_name = real_name
      if (email !== undefined) updateData.email = email
      if (phone !== undefined) updateData.phone = phone
      if (role !== undefined && ['admin', 'editor'].includes(role)) {
        updateData.role = role
      }
      if (status !== undefined && ['active', 'inactive'].includes(status)) {
        updateData.status = status
      }

      await admin.update(updateData)

      ctx.body = {
        code: 200,
        message: '管理员信息更新成功',
        data: {
          id: admin.id,
          username: admin.username,
          real_name: admin.real_name,
          email: admin.email,
          phone: admin.phone,
          role: admin.role,
          status: admin.status
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '更新管理员信息失败',
        error: error.message
      }
    }
  }
}

-- ========================================
-- 双冥想小程序测试数据插入脚本
-- 创建时间: 2025-08-11
-- 说明: 为所有表添加丰富的测试数据，支持API接口测试
-- ========================================

USE meditation_app;

-- =========================
-- 清空所有表数据（按依赖关系顺序）
-- =========================
-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 清空所有表数据
TRUNCATE TABLE plant_growth_records;
TRUNCATE TABLE plants;
TRUNCATE TABLE plan_history;
TRUNCATE TABLE plan_items;
TRUNCATE TABLE plans;
TRUNCATE TABLE meditation_content_tags;
TRUNCATE TABLE meditation_tags;
TRUNCATE TABLE user_streaks;
TRUNCATE TABLE user_meditation_stats;
TRUNCATE TABLE user_favorites;
TRUNCATE TABLE meditation_content;
TRUNCATE TABLE users;

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

SELECT '所有表数据已清空！' as message;

-- =========================
-- 1. 用户表测试数据
-- =========================
INSERT INTO users (openid, unionid, nickname, avatar_url, meditation_level, streak_days) VALUES
('openid_test_123', 'unionid_test_123', '测试用户', 'https://youke1.picui.cn/s1/2025/08/11/68999a8cbe3d0.png', 1, 0),
('openid_alice_456', 'unionid_alice_456', '爱丽丝', 'https://example.com/avatar/alice.jpg', 3, 15),
('openid_bob_789', 'unionid_bob_789', '小明', 'https://example.com/avatar/bob.jpg', 2, 7),
('openid_carol_101', 'unionid_carol_101', '小红', 'https://example.com/avatar/carol.jpg', 4, 30),
('openid_david_202', 'unionid_david_202', '大卫', 'https://example.com/avatar/david.jpg', 1, 3),
('openid_emma_303', 'unionid_emma_303', '艾玛', 'https://example.com/avatar/emma.jpg', 5, 45),
('openid_frank_404', 'unionid_frank_404', '弗兰克', 'https://example.com/avatar/frank.jpg', 2, 12);

-- =========================
-- 2. 冥想标签数据
-- =========================
INSERT INTO meditation_tags (name) VALUES 
('正念'), ('专注'), ('睡眠'), ('放松'), ('减压'), ('冥想入门'), 
('呼吸练习'), ('身体扫描'), ('慈心冥想'), ('行走冥想'), ('音乐冥想'), 
('自然声音'), ('白噪音'), ('雨声'), ('海浪声'), ('森林声音'),
('焦虑缓解'), ('情绪管理'), ('提升专注'), ('改善睡眠'), ('增强创造力');

-- =========================
-- 3. 冥想内容数据
-- =========================
-- 冥想课程系列
INSERT INTO meditation_content (type, sub_type, parent_id, title, description, cover_url, duration, tags_text, favorite_count) VALUES
-- 正念冥想系列课程
('meditation', 'course', NULL, '正念冥想入门系列', '适合初学者的正念冥想课程，帮助你建立冥想习惯', 'https://example.com/covers/mindfulness_course.jpg', 0, '正念,冥想入门,专注', 156),
('meditation', 'single', 1, '正念冥想第一课：观察呼吸', '学习如何观察自己的呼吸，建立正念基础', 'https://example.com/covers/breath1.jpg', 600, '正念,呼吸练习,冥想入门', 89),
('meditation', 'single', 1, '正念冥想第二课：身体扫描', '通过身体扫描练习提升身体觉知', 'https://example.com/covers/body_scan.jpg', 900, '正念,身体扫描,放松', 76),
('meditation', 'single', 1, '正念冥想第三课：情绪观察', '学习如何观察和接纳自己的情绪', 'https://example.com/covers/emotion.jpg', 720, '正念,情绪管理,减压', 65),

-- 专注力提升系列
('meditation', 'course', NULL, '专注力提升训练', '通过系统训练提升专注力和工作效率', 'https://example.com/covers/focus_course.jpg', 0, '专注,提升专注,工作效率', 134),
('meditation', 'single', 5, '专注力训练：单点专注', '练习将注意力集中在单一对象上', 'https://example.com/covers/single_focus.jpg', 480, '专注,提升专注', 58),
('meditation', 'single', 5, '专注力训练：数息法', '通过数息练习提升专注力', 'https://example.com/covers/counting_breath.jpg', 600, '专注,呼吸练习,提升专注', 42),

-- 减压放松系列
('meditation', 'course', NULL, '减压放松冥想', '帮助缓解压力，放松身心的冥想练习', 'https://example.com/covers/stress_relief.jpg', 0, '减压,放松,情绪管理', 198),
('meditation', 'single', 8, '深度放松冥想', '通过渐进式肌肉放松达到深度放松状态', 'https://example.com/covers/deep_relax.jpg', 1200, '放松,减压,身体扫描', 87),
('meditation', 'single', 8, '焦虑缓解冥想', '专门针对焦虑情绪的缓解练习', 'https://example.com/covers/anxiety_relief.jpg', 900, '焦虑缓解,减压,情绪管理', 93),

-- 慈心冥想系列
('meditation', 'course', NULL, '慈心冥想修习', '培养慈悲心和爱心的冥想练习', 'https://example.com/covers/loving_kindness.jpg', 0, '慈心冥想,情绪管理,爱心', 112),
('meditation', 'single', 11, '慈心冥想：对自己', '学习如何对自己发送慈爱', 'https://example.com/covers/self_love.jpg', 720, '慈心冥想,自爱,情绪管理', 67),
('meditation', 'single', 11, '慈心冥想：对他人', '将慈爱扩展到他人身上', 'https://example.com/covers/love_others.jpg', 840, '慈心冥想,爱心,人际关系', 54),

-- 单独的冥想练习
('meditation', 'single', NULL, '5分钟快速冥想', '适合忙碌生活的短时间冥想练习', 'https://example.com/covers/quick_meditation.jpg', 300, '正念,快速冥想,专注', 234),
('meditation', 'single', NULL, '行走冥想', '在行走中保持正念的练习方法', 'https://example.com/covers/walking_meditation.jpg', 900, '行走冥想,正念,户外练习', 145),
('meditation', 'single', NULL, '工作间隙冥想', '适合在工作间隙进行的简短练习', 'https://example.com/covers/work_break.jpg', 180, '专注,工作效率,减压', 178),

-- 睡眠辅助音频
('sleep', NULL, NULL, '深度睡眠引导', '帮助快速入睡的引导音频', 'https://example.com/covers/deep_sleep.jpg', 1800, '睡眠,放松,改善睡眠', 267),
('sleep', NULL, NULL, '午休小憩音频', '适合午休时间的短暂放松', 'https://example.com/covers/nap.jpg', 900, '睡眠,放松,午休', 156),
('sleep', NULL, NULL, '失眠缓解音频', '专门针对失眠问题的音频', 'https://example.com/covers/insomnia.jpg', 2400, '睡眠,失眠,改善睡眠', 189),

-- 自然声音
('sound', NULL, NULL, '雨声白噪音', '舒缓的雨声，帮助放松和专注', 'https://example.com/covers/rain.jpg', 3600, '雨声,白噪音,放松,专注', 345),
('sound', NULL, NULL, '海浪声', '平静的海浪声，营造宁静氛围', 'https://example.com/covers/ocean.jpg', 3600, '海浪声,自然声音,放松', 298),
('sound', NULL, NULL, '森林鸟鸣', '清晨森林中的鸟鸣声', 'https://example.com/covers/forest.jpg', 3600, '森林声音,鸟鸣,自然声音', 267),
('sound', NULL, NULL, '篝火声', '温暖的篝火燃烧声', 'https://example.com/covers/fire.jpg', 3600, '篝火声,温暖,放松', 198),
('sound', NULL, NULL, '山溪流水', '清澈山溪的流水声', 'https://example.com/covers/stream.jpg', 3600, '流水声,自然声音,宁静', 234);

-- =========================
-- 4. 冥想内容标签关联
-- =========================
INSERT INTO meditation_content_tags (meditation_id, tag_id) VALUES
-- 正念冥想入门系列
(1, 1), (1, 6), (1, 2),
(2, 1), (2, 7), (2, 6),
(3, 1), (3, 8), (3, 4),
(4, 1), (4, 18), (4, 5),
-- 专注力提升训练
(5, 2), (5, 19), 
(6, 2), (6, 19),
(7, 2), (7, 7), (7, 19),
-- 减压放松冥想
(8, 5), (8, 4), (8, 18),
(9, 4), (9, 5), (9, 8),
(10, 17), (10, 5), (10, 18),
-- 慈心冥想修习
(11, 9), (11, 18),
(12, 9), (12, 18),
(13, 9), (13, 18),
-- 单独练习
(14, 1), (14, 2),
(15, 10), (15, 1),
(16, 2), (16, 5),
-- 睡眠音频
(17, 3), (17, 4), (17, 20),
(18, 3), (18, 4),
(19, 3), (19, 20),
-- 自然声音
(20, 14), (20, 13), (20, 4), (20, 2),
(21, 15), (21, 12), (21, 4),
(22, 16), (22, 12), (22, 4),
(23, 12), (23, 4),
(24, 12), (24, 4);

-- =========================
-- 5. 用户收藏数据
-- =========================
INSERT INTO user_favorites (user_id, meditation_id) VALUES
-- 用户1的收藏（测试用户）
(1, 2), (1, 14), (1, 17), (1, 20),
-- 用户2的收藏（爱丽丝）
(2, 1), (2, 5), (2, 9), (2, 15), (2, 18), (2, 21),
-- 用户3的收藏（小明）
(3, 3), (3, 6), (3, 10), (3, 16), (3, 19),
-- 用户4的收藏（小红）
(4, 4), (4, 7), (4, 11), (4, 14), (4, 17), (4, 22), (4, 23),
-- 用户5的收藏（大卫）
(5, 8), (5, 12), (5, 20), (5, 24),
-- 用户6的收藏（艾玛）
(6, 1), (6, 2), (6, 13), (6, 15), (6, 18), (6, 21), (6, 22),
-- 用户7的收藏（弗兰克）
(7, 5), (7, 9), (7, 16), (7, 19), (7, 23);

-- =========================
-- 6. 多肉数据
-- =========================
INSERT INTO plants (user_id, species, energy_value, level) VALUES
-- 用户1的多肉（测试用户）
(1, '仙人掌', 0, 1),
-- 用户2的多肉（爱丽丝）
(2, '多肉植物', 150, 2),
(2, '仙人球', 80, 1),
-- 用户3的多肉（小明）
(3, '石莲花', 220, 3),
-- 用户4的多肉（小红）
(4, '虎皮兰', 350, 4),
(4, '芦荟', 120, 2),
(4, '仙人掌', 90, 1),
-- 用户5的多肉（大卫）
(5, '多肉植物', 45, 1),
-- 用户6的多肉（艾玛）
(6, '石莲花', 280, 3),
(6, '仙人球', 160, 2),
-- 用户7的多肉（弗兰克）
(7, '虎皮兰', 200, 2),
(7, '芦荟', 75, 1);

-- =========================
-- 7. 多肉成长记录
-- =========================
INSERT INTO plant_growth_records (plant_id, change_value, reason) VALUES
-- 用户2的多肉成长记录
(2, 10, '完成正念冥想练习'),
(2, 15, '完成专注力训练'),
(2, 20, '连续冥想3天奖励'),
(2, 25, '完成慈心冥想'),
(2, 30, '连续冥想7天奖励'),
(2, 20, '完成减压冥想'),
(2, 30, '连续冥想15天奖励'),
(3, 10, '完成快速冥想'),
(3, 15, '完成身体扫描'),
(3, 20, '完成情绪观察练习'),
(3, 25, '连续冥想5天奖励'),
-- 用户3的多肉成长记录
(4, 20, '完成深度放松冥想'),
(4, 25, '完成焦虑缓解练习'),
(4, 30, '连续冥想10天奖励'),
(4, 35, '完成慈心冥想系列'),
(4, 40, '连续冥想20天奖励'),
(4, 45, '完成专注力提升训练'),
(4, 50, '连续冥想30天奖励'),
-- 用户4的多肉成长记录
(5, 30, '完成正念冥想入门系列'),
(5, 35, '完成专注力提升训练'),
(5, 40, '完成减压放松冥想'),
(5, 45, '完成慈心冥想修习'),
(5, 50, '连续冥想30天奖励'),
(5, 55, '完成高级冥想练习'),
(5, 60, '连续冥想45天奖励'),
(6, 15, '完成工作间隙冥想'),
(6, 20, '完成行走冥想'),
(6, 25, '连续冥想7天奖励'),
(6, 30, '完成深度睡眠练习'),
(7, 10, '完成5分钟快速冥想'),
(7, 15, '完成呼吸练习'),
(7, 20, '连续冥想3天奖励'),
-- 用户6的多肉成长记录
(8, 25, '完成正念冥想练习'),
(8, 30, '完成专注力训练'),
(8, 35, '连续冥想15天奖励'),
(8, 40, '完成减压冥想'),
(8, 45, '连续冥想25天奖励'),
(9, 20, '完成身体扫描'),
(9, 25, '完成情绪管理练习'),
(9, 30, '连续冥想10天奖励'),
-- 用户7的多肉成长记录
(10, 20, '完成专注力训练'),
(10, 25, '完成减压冥想'),
(10, 30, '连续冥想12天奖励'),
(10, 35, '完成慈心冥想'),
(11, 10, '完成快速冥想'),
(11, 15, '完成呼吸练习'),
(11, 20, '连续冥想5天奖励');

-- =========================
-- 8. 用户冥想统计数据
-- =========================
INSERT INTO user_meditation_stats (user_id, period_type, period_date, meditation_duration, energy_gained, tasks_completed) VALUES
-- 用户1的统计数据（最近7天）
(1, 'day', '2025-08-05', 600, 10, 1),
(1, 'day', '2025-08-06', 0, 0, 0),
(1, 'day', '2025-08-07', 900, 15, 1),
(1, 'day', '2025-08-08', 1200, 20, 2),
(1, 'day', '2025-08-09', 600, 10, 1),
(1, 'day', '2025-08-10', 1800, 30, 3),
(1, 'day', '2025-08-11', 300, 5, 1),
-- 用户2的统计数据
(2, 'day', '2025-08-05', 1800, 30, 3),
(2, 'day', '2025-08-06', 1200, 20, 2),
(2, 'day', '2025-08-07', 2400, 40, 4),
(2, 'day', '2025-08-08', 900, 15, 1),
(2, 'day', '2025-08-09', 1800, 30, 3),
(2, 'day', '2025-08-10', 2100, 35, 3),
(2, 'day', '2025-08-11', 1500, 25, 2),
-- 用户3的统计数据
(3, 'day', '2025-08-05', 1200, 20, 2),
(3, 'day', '2025-08-06', 900, 15, 1),
(3, 'day', '2025-08-07', 0, 0, 0),
(3, 'day', '2025-08-08', 1800, 30, 3),
(3, 'day', '2025-08-09', 600, 10, 1),
(3, 'day', '2025-08-10', 1500, 25, 2),
(3, 'day', '2025-08-11', 2100, 35, 3),
-- 用户4的统计数据
(4, 'day', '2025-08-05', 2400, 40, 4),
(4, 'day', '2025-08-06', 1800, 30, 3),
(4, 'day', '2025-08-07', 2700, 45, 5),
(4, 'day', '2025-08-08', 1500, 25, 2),
(4, 'day', '2025-08-09', 3000, 50, 5),
(4, 'day', '2025-08-10', 2100, 35, 3),
(4, 'day', '2025-08-11', 1800, 30, 3),
-- 周统计数据
(2, 'week', '2025-08-05', 12300, 205, 18),
(3, 'week', '2025-08-05', 8100, 135, 12),
(4, 'week', '2025-08-05', 15300, 255, 25),
-- 月统计数据
(2, 'month', '2025-08-01', 45600, 760, 68),
(3, 'month', '2025-08-01', 32400, 540, 48),
(4, 'month', '2025-08-01', 58200, 970, 95);

-- =========================
-- 9. 用户坚持记录
-- =========================
INSERT INTO user_streaks (user_id, streak_start, streak_end, total_days) VALUES
(2, '2025-07-28', '2025-08-11', 15),
(3, '2025-08-05', '2025-08-11', 7),
(4, '2025-07-12', '2025-08-11', 30),
(6, '2025-07-27', '2025-08-11', 45),
(7, '2025-07-30', '2025-08-11', 12);

-- =========================
-- 10. 计划数据
-- =========================
INSERT INTO plans (user_id, plan_date) VALUES
-- 用户1的计划
(1, '2025-08-09'),
(1, '2025-08-10'),
(1, '2025-08-11'),
(1, '2025-08-12'),
-- 用户2的计划
(2, '2025-08-08'),
(2, '2025-08-09'),
(2, '2025-08-10'),
(2, '2025-08-11'),
(2, '2025-08-12'),
(2, '2025-08-13'),
-- 用户3的计划
(3, '2025-08-10'),
(3, '2025-08-11'),
(3, '2025-08-12'),
-- 用户4的计划
(4, '2025-08-09'),
(4, '2025-08-10'),
(4, '2025-08-11'),
(4, '2025-08-12'),
(4, '2025-08-13'),
(4, '2025-08-14');

-- =========================
-- 11. 计划项数据
-- =========================
INSERT INTO plan_items (plan_id, meditation_id, seq, completed, completed_at) VALUES
-- 用户1的计划项（plan_id: 1-4）
(1, 14, 1, 1, '2025-08-09 07:30:00'),
(1, 2, 2, 1, '2025-08-09 20:15:00'),
(2, 3, 1, 1, '2025-08-10 08:00:00'),
(2, 17, 2, 1, '2025-08-10 22:30:00'),
(2, 20, 3, 1, '2025-08-10 14:20:00'),
(3, 6, 1, 1, '2025-08-11 07:45:00'),
(3, 9, 2, 0, NULL),
(4, 15, 1, 0, NULL),
(4, 18, 2, 0, NULL),

-- 用户2的计划项（plan_id: 5-10）
(5, 1, 1, 1, '2025-08-08 06:30:00'),
(5, 5, 2, 1, '2025-08-08 19:45:00'),
(6, 8, 1, 1, '2025-08-09 07:15:00'),
(6, 11, 2, 1, '2025-08-09 20:30:00'),
(7, 12, 1, 1, '2025-08-10 08:30:00'),
(7, 16, 2, 1, '2025-08-10 12:15:00'),
(7, 21, 3, 1, '2025-08-10 21:00:00'),
(8, 13, 1, 1, '2025-08-11 07:00:00'),
(8, 19, 2, 0, NULL),
(9, 4, 1, 0, NULL),
(9, 10, 2, 0, NULL),
(9, 22, 3, 0, NULL),
(10, 7, 1, 0, NULL),
(10, 24, 2, 0, NULL),

-- 用户3的计划项（plan_id: 11-13）
(11, 14, 1, 1, '2025-08-10 09:00:00'),
(11, 20, 2, 1, '2025-08-10 15:30:00'),
(12, 2, 1, 1, '2025-08-11 08:15:00'),
(12, 17, 2, 0, NULL),
(13, 6, 1, 0, NULL),
(13, 23, 2, 0, NULL),

-- 用户4的计划项（plan_id: 14-19）
(14, 1, 1, 1, '2025-08-09 06:00:00'),
(14, 8, 2, 1, '2025-08-09 18:30:00'),
(15, 11, 1, 1, '2025-08-10 07:30:00'),
(15, 15, 2, 1, '2025-08-10 13:45:00'),
(15, 18, 3, 1, '2025-08-10 22:15:00'),
(16, 4, 1, 1, '2025-08-11 08:00:00'),
(16, 9, 2, 1, '2025-08-11 19:20:00'),
(16, 21, 3, 0, NULL),
(17, 12, 1, 0, NULL),
(17, 16, 2, 0, NULL),
(18, 7, 1, 0, NULL),
(18, 13, 2, 0, NULL),
(18, 24, 3, 0, NULL),
(19, 10, 1, 0, NULL),
(19, 22, 2, 0, NULL);

-- =========================
-- 12. 计划历史记录
-- =========================
INSERT INTO plan_history (user_id, meditation_id, completed_at) VALUES
-- 用户1的历史记录
(1, 14, '2025-08-09 07:30:00'),
(1, 2, '2025-08-09 20:15:00'),
(1, 3, '2025-08-10 08:00:00'),
(1, 17, '2025-08-10 22:30:00'),
(1, 20, '2025-08-10 14:20:00'),
(1, 6, '2025-08-11 07:45:00'),

-- 用户2的历史记录
(2, 1, '2025-08-08 06:30:00'),
(2, 5, '2025-08-08 19:45:00'),
(2, 8, '2025-08-09 07:15:00'),
(2, 11, '2025-08-09 20:30:00'),
(2, 12, '2025-08-10 08:30:00'),
(2, 16, '2025-08-10 12:15:00'),
(2, 21, '2025-08-10 21:00:00'),
(2, 13, '2025-08-11 07:00:00'),

-- 用户3的历史记录
(3, 14, '2025-08-10 09:00:00'),
(3, 20, '2025-08-10 15:30:00'),
(3, 2, '2025-08-11 08:15:00'),

-- 用户4的历史记录
(4, 1, '2025-08-09 06:00:00'),
(4, 8, '2025-08-09 18:30:00'),
(4, 11, '2025-08-10 07:30:00'),
(4, 15, '2025-08-10 13:45:00'),
(4, 18, '2025-08-10 22:15:00'),
(4, 4, '2025-08-11 08:00:00'),
(4, 9, '2025-08-11 19:20:00');

-- =========================
-- 数据插入完成提示
-- =========================
SELECT '测试数据插入完成！' as message;

-- 统计信息
SELECT
    '用户数量' as item, COUNT(*) as count FROM users
UNION ALL
SELECT
    '冥想内容数量' as item, COUNT(*) as count FROM meditation_content
UNION ALL
SELECT
    '标签数量' as item, COUNT(*) as count FROM meditation_tags
UNION ALL
SELECT
    '收藏记录数量' as item, COUNT(*) as count FROM user_favorites
UNION ALL
SELECT
    '多肉数量' as item, COUNT(*) as count FROM plants
UNION ALL
SELECT
    '成长记录数量' as item, COUNT(*) as count FROM plant_growth_records
UNION ALL
SELECT
    '计划数量' as item, COUNT(*) as count FROM plans
UNION ALL
SELECT
    '计划项数量' as item, COUNT(*) as count FROM plan_items
UNION ALL
SELECT
    '历史记录数量' as item, COUNT(*) as count FROM plan_history
UNION ALL
SELECT
    '统计数据数量' as item, COUNT(*) as count FROM user_meditation_stats;

{"openapi": "3.0.0", "info": {"title": "双冥想小程序后端API", "version": "1.0.0", "description": "基于Koa2的冥想应用后端API接口文档"}, "servers": [{"url": "http://localhost:3000/api", "description": "开发环境"}], "components": {"securitySchemes": {"Bearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT token, 格式: Bearer <token>"}}, "schemas": {"ApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "object", "description": "响应数据"}}}, "PaginatedResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "description": "总数量"}, "page": {"type": "integer", "description": "当前页码"}, "limit": {"type": "integer", "description": "每页数量"}, "items": {"type": "array", "items": {"type": "object"}, "description": "数据列表"}}}}}, "User": {"type": "object", "properties": {"id": {"type": "integer", "description": "用户ID"}, "openid": {"type": "string", "description": "微信openid"}, "unionid": {"type": "string", "description": "微信unionid"}, "nickname": {"type": "string", "description": "用户昵称"}, "avatar_url": {"type": "string", "description": "头像URL"}, "meditation_level": {"type": "integer", "description": "冥想等级"}, "streak_days": {"type": "integer", "description": "连续天数"}}}, "MeditationContent": {"type": "object", "properties": {"id": {"type": "integer", "description": "内容ID"}, "title": {"type": "string", "description": "标题"}, "description": {"type": "string", "description": "描述"}, "type": {"type": "string", "enum": ["audio", "video", "text"], "description": "内容类型"}, "sub_type": {"type": "string", "description": "子类型"}, "duration": {"type": "integer", "description": "时长(秒)"}, "cover_url": {"type": "string", "description": "封面图片URL"}, "content_url": {"type": "string", "description": "内容URL"}, "favorite_count": {"type": "integer", "description": "收藏数"}}}, "Plant": {"type": "object", "properties": {"id": {"type": "integer", "description": "多肉ID"}, "user_id": {"type": "integer", "description": "用户ID"}, "species": {"type": "string", "description": "品种"}, "energy_value": {"type": "integer", "description": "能量值"}, "level": {"type": "integer", "description": "等级"}}}}}, "paths": {"/meditation/list": {"get": {"tags": ["冥想模块"], "summary": "获取冥想内容列表", "description": "获取冥想内容列表，支持分页和筛选", "parameters": [{"in": "query", "name": "type", "schema": {"type": "string", "enum": ["audio", "video", "text"]}, "description": "内容类型"}, {"in": "query", "name": "sub_type", "schema": {"type": "string"}, "description": "子类型"}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "每页数量"}, {"in": "query", "name": "search", "schema": {"type": "string"}, "description": "搜索关键词"}, {"in": "query", "name": "parent_id", "schema": {"type": "integer"}, "description": "父级内容ID"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/meditation/{id}": {"get": {"tags": ["冥想模块"], "summary": "获取冥想内容详情", "description": "获取指定ID的冥想内容详情", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "冥想内容ID"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {"allOf": [{"$ref": "#/components/schemas/MeditationContent"}, {"type": "object", "properties": {"is_favorited": {"type": "boolean", "description": "是否已收藏"}, "parent": {"$ref": "#/components/schemas/MeditationContent"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/MeditationContent"}}}}]}}}}}}, "404": {"description": "冥想内容不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/meditation/{id}/favorite": {"post": {"tags": ["冥想模块"], "summary": "收藏/取消收藏冥想内容", "description": "切换指定冥想内容的收藏状态", "security": [{"Bearer": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "冥想内容ID"}], "responses": {"200": {"description": "操作成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "收藏成功"}, "data": {"type": "object", "properties": {"is_favorited": {"type": "boolean", "description": "收藏状态"}}}}}}}}, "404": {"description": "冥想内容不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/meditation/search": {"get": {"tags": ["冥想模块"], "summary": "搜索冥想内容", "description": "根据关键词搜索冥想内容，支持标题、描述、标签搜索", "parameters": [{"in": "query", "name": "keyword", "required": true, "schema": {"type": "string"}, "description": "搜索关键词"}, {"in": "query", "name": "type", "schema": {"type": "string", "enum": ["audio", "video", "text"]}, "description": "内容类型筛选"}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "每页数量"}], "responses": {"200": {"description": "搜索成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse"}}}}, "400": {"description": "搜索关键词不能为空", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/meditation/tags": {"get": {"tags": ["冥想模块"], "summary": "获取冥想标签列表", "description": "获取所有可用的冥想标签", "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "标签ID"}, "name": {"type": "string", "description": "标签名称"}, "created_at": {"type": "string", "format": "date-time", "description": "创建时间"}}}}}}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/plan/daily": {"get": {"tags": ["计划模块"], "summary": "获取每日计划", "description": "获取指定日期前后三天的计划列表", "security": [{"Bearer": []}], "parameters": [{"in": "query", "name": "date", "schema": {"type": "string", "format": "date", "default": "当前日期"}, "description": "目标日期 (YYYY-MM-DD)"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "计划ID"}, "user_id": {"type": "integer", "description": "用户ID"}, "plan_date": {"type": "string", "format": "date", "description": "计划日期"}, "items": {"type": "array", "description": "计划项列表"}}}}}}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/plan/add": {"post": {"tags": ["计划模块"], "summary": "添加冥想内容到计划", "description": "将指定的冥想内容添加到用户的计划中", "security": [{"Bearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["meditation_id"], "properties": {"meditation_id": {"type": "integer", "description": "冥想内容ID", "example": 1}, "plan_date": {"type": "string", "format": "date", "description": "计划日期，默认为当前日期", "example": "2024-01-15"}}}}}}, "responses": {"200": {"description": "添加成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "添加到计划成功"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "description": "计划项ID"}, "plan_id": {"type": "integer", "description": "计划ID"}, "meditation_id": {"type": "integer", "description": "冥想内容ID"}, "seq": {"type": "integer", "description": "序号"}}}}}}}}, "400": {"description": "请求参数错误或内容已在计划中", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/plan/item/{id}": {"delete": {"tags": ["计划模块"], "summary": "从计划中删除冥想内容", "description": "从用户计划中删除指定的计划项", "security": [{"Bearer": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "计划项ID"}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "从计划中删除成功"}}}}}}, "404": {"description": "计划项不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/plan/item/{id}/complete": {"put": {"tags": ["计划模块"], "summary": "完成计划项", "description": "标记指定计划项为已完成，并添加到历史记录", "security": [{"Bearer": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "计划项ID"}], "responses": {"200": {"description": "完成成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "计划项完成"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "description": "计划项ID"}, "completed": {"type": "boolean", "description": "完成状态"}, "completed_at": {"type": "string", "format": "date-time", "description": "完成时间"}}}}}}}}, "404": {"description": "计划项不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/plan/history": {"get": {"tags": ["计划模块"], "summary": "获取历史计划和完成情况", "description": "获取用户的计划完成历史记录", "security": [{"Bearer": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "每页数量"}, {"in": "query", "name": "start_date", "schema": {"type": "string", "format": "date"}, "description": "开始日期"}, {"in": "query", "name": "end_date", "schema": {"type": "string", "format": "date"}, "description": "结束日期"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/plan/stats": {"get": {"tags": ["计划模块"], "summary": "获取计划统计", "description": "获取用户计划的统计数据，包括完成率等", "security": [{"Bearer": []}], "parameters": [{"in": "query", "name": "start_date", "schema": {"type": "string", "format": "date"}, "description": "开始日期"}, {"in": "query", "name": "end_date", "schema": {"type": "string", "format": "date"}, "description": "结束日期"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"total_plans": {"type": "integer", "description": "总计划数"}, "total_items": {"type": "integer", "description": "总计划项数"}, "completed_items": {"type": "integer", "description": "已完成计划项数"}, "completion_rate": {"type": "string", "description": "完成率（百分比）", "example": "85.50"}}}}}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/plant/list": {"get": {"tags": ["多肉模块"], "summary": "获取多肉列表", "description": "获取当前用户的多肉列表", "security": [{"Bearer": []}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Plant"}}}}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/plant/{id}": {"get": {"tags": ["多肉模块"], "summary": "获取多肉详情", "description": "获取指定多肉的详细信息和成长记录", "security": [{"Bearer": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "多肉ID"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"plant": {"$ref": "#/components/schemas/Plant"}, "growth_records": {"type": "array", "items": {"$ref": "#/components/schemas/PlantGrowthRecord"}}}}}}}}}, "404": {"description": "多肉不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/plant/{id}/energy": {"post": {"tags": ["多肉模块"], "summary": "为多肉增加能量", "description": "为指定多肉增加能量值，可能触发升级", "security": [{"Bearer": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "多肉ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["energy_value"], "properties": {"energy_value": {"type": "integer", "minimum": 1, "description": "增加的能量值", "example": 10}, "reason": {"type": "string", "description": "增加能量的原因", "example": "完成冥想"}}}}}}, "responses": {"200": {"description": "能量添加成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "能量添加成功"}, "data": {"type": "object", "properties": {"plant": {"$ref": "#/components/schemas/Plant"}, "level_up": {"type": "boolean", "description": "是否升级了"}}}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "404": {"description": "多肉不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/plant/create": {"post": {"tags": ["多肉模块"], "summary": "创建新多肉", "description": "为当前用户创建一个新的多肉", "security": [{"Bearer": []}], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"species": {"type": "string", "description": "多肉品种", "default": "succulent", "example": "succulent"}}}}}}, "responses": {"200": {"description": "多肉创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "多肉创建成功"}, "data": {"$ref": "#/components/schemas/Plant"}}}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/plant/{id}/records": {"get": {"tags": ["多肉模块"], "summary": "获取多肉成长记录", "description": "获取指定多肉的成长记录列表", "security": [{"Bearer": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "多肉ID"}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 20}, "description": "每页数量"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse"}}}}, "404": {"description": "多肉不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/upload": {"post": {"tags": ["文件上传"], "summary": "上传文件", "description": "上传文件到服务器，支持图片等文件类型", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "要上传的文件"}}}}}}, "responses": {"200": {"description": "上传成功", "content": {"text/plain": {"schema": {"type": "string", "description": "文件访问URL", "example": "http://localhost:3000/assets/uploads/123456789.jpg"}}}}, "400": {"description": "上传失败", "content": {"text/plain": {"schema": {"type": "string", "example": "error|save error"}}}}}}, "options": {"tags": ["文件上传"], "summary": "预检请求", "description": "处理跨域预检请求", "responses": {"200": {"description": "预检成功", "content": {"text/plain": {"schema": {"type": "string", "example": "options OK"}}}}}}}, "/public/user/login": {"post": {"tags": ["用户模块"], "summary": "微信小程序用户登录", "description": "微信小程序用户登录，支持新用户自动注册", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["openid"], "properties": {"openid": {"type": "string", "description": "微信openid", "example": "openid_test_123"}, "unionid": {"type": "string", "description": "微信unionid", "example": "unionid_test_123"}, "nickname": {"type": "string", "description": "用户昵称", "example": "欢欢"}, "avatar_url": {"type": "string", "description": "头像URL", "example": "https://example.com/avatar.jpg"}}}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "登录成功"}, "data": {"type": "object", "properties": {"token": {"type": "string", "description": "JWT token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "user": {"$ref": "#/components/schemas/User"}}}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/user/profile": {"get": {"tags": ["用户模块"], "summary": "获取用户基础信息", "description": "获取当前登录用户的基础信息", "security": [{"Bearer": []}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"meditation_level": {"type": "integer", "description": "冥想等级", "example": 2}, "streak_days": {"type": "integer", "description": "连续天数", "example": 5}, "plant_count": {"type": "integer", "description": "多肉数量", "example": 2}}}}}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/user/favorites": {"get": {"tags": ["用户模块"], "summary": "获取用户收藏列表", "description": "获取当前用户收藏的冥想内容列表", "security": [{"Bearer": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10}, "description": "每页数量"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/user/plants": {"get": {"tags": ["用户模块"], "summary": "获取用户多肉列表", "description": "获取当前用户的多肉列表", "security": [{"Bearer": []}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Plant"}}}}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/user/meditation-stats": {"get": {"tags": ["用户模块"], "summary": "获取冥想统计数据", "description": "获取用户的冥想统计数据", "security": [{"Bearer": []}], "parameters": [{"in": "query", "name": "period_type", "required": true, "schema": {"type": "string", "enum": ["day", "week", "month", "year"]}, "description": "统计周期类型"}, {"in": "query", "name": "start_date", "schema": {"type": "string", "format": "date"}, "description": "开始日期"}, {"in": "query", "name": "end_date", "schema": {"type": "string", "format": "date"}, "description": "结束日期"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"type": "object", "properties": {"user_id": {"type": "integer", "description": "用户ID"}, "period_type": {"type": "string", "description": "周期类型"}, "period_date": {"type": "string", "format": "date", "description": "统计日期"}, "meditation_count": {"type": "integer", "description": "冥想次数"}, "total_duration": {"type": "integer", "description": "总时长"}}}}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}}, "tags": []}
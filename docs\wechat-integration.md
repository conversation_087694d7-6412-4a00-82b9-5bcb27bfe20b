# 微信小程序集成说明

## 概述

本项目已集成微信小程序登录功能，包括通过微信code换取openid的接口。

## 配置说明

### 1. 微信小程序配置

在 `src/config.js` 文件中配置微信小程序的相关信息：

```javascript
export const WeChat = {
  appid: 'your_wechat_appid', // 微信小程序AppID
  secret: 'your_wechat_secret', // 微信小程序AppSecret
  code2session_url: 'https://api.weixin.qq.com/sns/jscode2session' // 微信code2session接口地址
}
```

**注意：** 请将 `your_wechat_appid` 和 `your_wechat_secret` 替换为您实际的微信小程序AppID和AppSecret。

### 2. 环境变量配置（推荐）

为了安全起见，建议将敏感信息配置在环境变量中：

```bash
# .env 文件
WECHAT_APPID=your_wechat_appid
WECHAT_SECRET=your_wechat_secret
```

然后在配置文件中使用：

```javascript
export const WeChat = {
  appid: process.env.WECHAT_APPID || 'your_wechat_appid',
  secret: process.env.WECHAT_SECRET || 'your_wechat_secret',
  code2session_url: 'https://api.weixin.qq.com/sns/jscode2session'
}
```

## API接口说明

### 1. 微信code换取openid

**接口地址：** `POST /api/public/user/code2session`

**请求参数：**
```json
{
  "code": "微信小程序登录时获取的code"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "获取openid成功",
  "data": {
    "openid": "openid_test_123",
    "session_key": "session_key_123",
    "unionid": "unionid_test_123"
  }
}
```

### 2. 用户登录

**接口地址：** `POST /api/public/user/login`

**请求参数：**
```json
{
  "openid": "微信openid",
  "unionid": "微信unionid (可选)",
  "nickname": "用户昵称 (可选)",
  "avatar_url": "头像URL (可选)"
}
```

## 小程序端使用示例

```javascript
// 小程序端登录流程
async function login() {
  try {
    // 1. 获取微信登录code
    const loginRes = await wx.login()
    const code = loginRes.code
    
    // 2. 调用后端接口换取openid
    const codeRes = await wx.request({
      url: 'https://your-domain.com/api/public/user/code2session',
      method: 'POST',
      data: { code }
    })
    
    const { openid, session_key } = codeRes.data.data
    
    // 3. 获取用户信息（如果需要）
    const userInfo = await wx.getUserProfile({
      desc: '用于完善用户资料'
    })
    
    // 4. 调用登录接口
    const loginResult = await wx.request({
      url: 'https://your-domain.com/api/public/user/login',
      method: 'POST',
      data: {
        openid,
        nickname: userInfo.userInfo.nickName,
        avatar_url: userInfo.userInfo.avatarUrl
      }
    })
    
    // 5. 保存token
    const { token } = loginResult.data.data
    wx.setStorageSync('token', token)
    
    console.log('登录成功')
  } catch (error) {
    console.error('登录失败:', error)
  }
}
```

## 安全注意事项

1. **保护AppSecret：** 微信小程序的AppSecret非常重要，不要在前端代码中暴露，只能在服务器端使用。

2. **HTTPS：** 生产环境中必须使用HTTPS协议。

3. **session_key安全：** session_key用于解密微信数据，需要妥善保管，不要传输给前端。

4. **token过期：** JWT token设置了30天过期时间，需要在前端处理token过期的情况。

## 错误处理

常见错误码：
- `40013`: 不合法的AppID
- `40014`: 不合法的access_token
- `40029`: code无效
- `45011`: 频率限制，每个用户每分钟100次

## 测试

运行测试：
```bash
npm test
```

测试文件位于 `test/user.test.js`，包含了code2session和login接口的基本测试用例。

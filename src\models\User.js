import Sequelize from 'sequelize'
import sequelize from '../lib/sequelize'

const User = sequelize.define('users', {
  id: {
    type: Sequelize.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  openid: {
    type: Sequelize.STRING(64),
    allowNull: false,
    unique: true,
    comment: '微信openid'
  },
  unionid: {
    type: Sequelize.STRING(64),
    allowNull: true,
    comment: '微信unionid'
  },
  nickname: {
    type: Sequelize.STRING(128),
    allowNull: true,
    comment: '昵称'
  },
  avatar_url: {
    type: Sequelize.STRING(512),
    allowNull: true,
    comment: '头像URL'
  },
  meditation_level: {
    type: Sequelize.INTEGER,
    defaultValue: 1,
    comment: '冥想等级'
  },
  streak_days: {
    type: Sequelize.INTEGER,
    defaultValue: 0,
    comment: '连续坚持天数'
  },
  created_at: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW,
    comment: '创建时间'
  },
  updated_at: {
    type: Sequelize.DATE,
    allowNull: true,
    comment: '更新时间'
  }
}, {
  timestamps: false,
  tableName: 'users'
})

export default User
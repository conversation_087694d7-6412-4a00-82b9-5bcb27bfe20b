import Sequelize from 'sequelize'
import sequelize from '../lib/sequelize'

const PlanItem = sequelize.define('plan_items', {
  id: {
    type: Sequelize.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  plan_id: {
    type: Sequelize.BIGINT,
    allowNull: false
  },
  meditation_id: {
    type: Sequelize.BIGINT,
    allowNull: false
  },
  seq: {
    type: Sequelize.INTEGER,
    defaultValue: 0,
    comment: '顺序'
  },
  completed: {
    type: Sequelize.BOOLEAN,
    defaultValue: false,
    comment: '是否完成'
  },
  completed_at: {
    type: Sequelize.DATE,
    allowNull: true
  }
}, {
  timestamps: false,
  tableName: 'plan_items'
})

export default PlanItem
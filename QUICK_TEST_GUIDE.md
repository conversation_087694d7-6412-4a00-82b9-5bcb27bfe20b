# 后台管理系统快速测试指南

## 前置准备

### 1. 确保数据库已更新
```bash
mysql -u your_username -p meditation_app < admin_system_database_changes.sql
```

### 2. 安装依赖
```bash
npm install bcrypt
```

### 3. 重启应用
```bash
npm restart
```

## 快速测试步骤

### 第一步：管理员登录测试

在 `pkslow.http` 文件中执行：

```http
### 32. 管理员登录
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

**预期结果：**
- 返回状态码 200
- 获得 adminToken
- 将 token 复制到文件顶部的 `@adminToken` 变量中

### 第二步：验证管理员权限

```http
### 33. 获取管理员信息
GET {{baseUrl}}/admin/profile
Authorization: Bearer {{adminToken}}
```

**预期结果：**
- 返回管理员基本信息
- 角色为 `super_admin`

### 第三步：用户管理测试

```http
### 37. 获取用户列表
GET {{baseUrl}}/admin/users?page=1&limit=10
Authorization: Bearer {{adminToken}}
```

**预期结果：**
- 返回用户列表
- 包含分页信息
- 显示用户的冥想等级、连续天数等信息

### 第四步：内容管理测试

```http
### 45. 获取冥想内容列表（管理端）
GET {{baseUrl}}/admin/meditation/contents?page=1&limit=10
Authorization: Bearer {{adminToken}}
```

**预期结果：**
- 返回冥想内容列表
- 包含管理端特有的状态信息

### 第五步：创建测试内容

```http
### 46. 创建冥想内容
POST {{baseUrl}}/admin/meditation/contents
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "title": "测试冥想内容",
  "description": "这是一个测试内容",
  "type": "meditation",
  "sub_type": "single",
  "duration": 600,
  "status": "published"
}
```

**预期结果：**
- 成功创建内容
- 返回创建的内容信息

### 第六步：标签管理测试

```http
### 51. 创建标签
POST {{baseUrl}}/admin/meditation/tags
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "测试标签"
}
```

**预期结果：**
- 成功创建标签
- 返回标签信息

### 第七步：多肉品种管理测试

```http
### 55. 获取多肉品种列表
GET {{baseUrl}}/admin/plants/species?page=1&limit=10
Authorization: Bearer {{adminToken}}
```

**预期结果：**
- 返回多肉品种列表
- 包含默认的多肉品种配置

### 第八步：统计数据测试

```http
### 40. 获取用户统计数据
GET {{baseUrl}}/admin/users/statistics
Authorization: Bearer {{adminToken}}
```

**预期结果：**
- 返回用户统计信息
- 包含等级分布、注册趋势等数据

## 错误场景测试

### 测试无效登录
```http
### 64. 管理员登录 - 错误密码
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "wrong_password"
}
```

**预期结果：**
- 返回状态码 401
- 提示用户名或密码错误

### 测试权限控制
```http
### 66. 无权限访问管理员列表（使用普通用户token）
GET {{baseUrl}}/admin/admins
Authorization: Bearer {{token}}
```

**预期结果：**
- 返回状态码 401 或 403
- 提示权限不足

## 常见问题排查

### 1. 登录失败
**问题：** 管理员登录返回 401 错误

**解决方案：**
- 检查数据库中是否存在管理员记录
- 确认密码是否正确（默认：admin123）
- 检查 bcrypt 依赖是否正确安装

### 2. Token 验证失败
**问题：** 接口返回 401 "token验证失败"

**解决方案：**
- 检查 JWT 密钥文件是否存在
- 确认 token 格式是否正确
- 检查 token 是否过期

### 3. 权限不足
**问题：** 接口返回 403 "权限不足"

**解决方案：**
- 确认当前管理员角色
- 检查接口所需的权限级别
- 使用超级管理员账户测试

### 4. 数据库连接错误
**问题：** 接口返回 500 数据库相关错误

**解决方案：**
- 检查数据库连接配置
- 确认新增的表是否创建成功
- 检查表结构是否正确

## 生产环境部署检查清单

- [ ] 数据库变更脚本已执行
- [ ] bcrypt 依赖已安装
- [ ] 默认管理员密码已修改
- [ ] JWT 密钥已配置
- [ ] 文件上传路径已配置
- [ ] HTTPS 已启用
- [ ] 日志记录已配置
- [ ] 备份策略已制定

## 性能测试建议

### 1. 并发测试
使用工具如 Apache Bench 或 wrk 测试管理端接口的并发性能：

```bash
# 测试用户列表接口
ab -n 1000 -c 10 -H "Authorization: Bearer YOUR_TOKEN" http://localhost:3000/admin/users
```

### 2. 大数据量测试
- 创建大量测试数据
- 测试分页性能
- 测试搜索功能性能

### 3. 内存使用监控
- 监控长时间运行的内存使用情况
- 检查是否存在内存泄漏

## 安全测试建议

### 1. SQL 注入测试
在搜索参数中尝试 SQL 注入攻击：
```
GET /admin/users?search=' OR '1'='1
```

### 2. XSS 测试
在创建内容时尝试插入脚本：
```json
{
  "title": "<script>alert('xss')</script>",
  "description": "测试XSS"
}
```

### 3. 权限绕过测试
- 尝试使用过期 token
- 尝试使用普通用户 token 访问管理端
- 尝试访问超出权限范围的接口

## 监控和日志

建议在生产环境中添加以下监控：

1. **API 调用监控**
   - 请求频率
   - 响应时间
   - 错误率

2. **安全监控**
   - 登录失败次数
   - 异常操作记录
   - IP 访问频率

3. **业务监控**
   - 管理员操作记录
   - 数据变更记录
   - 系统资源使用情况

## 总结

通过以上测试步骤，您可以快速验证后台管理系统的基本功能。建议在正式使用前：

1. 完成所有基础功能测试
2. 执行错误场景测试
3. 进行安全测试
4. 配置监控和日志
5. 制定备份和恢复策略

如果在测试过程中遇到问题，请参考 `ADMIN_SYSTEM_SETUP.md` 文件中的详细说明。

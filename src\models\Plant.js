import Sequelize from 'sequelize'
import sequelize from '../lib/sequelize'

const Plant = sequelize.define('plants', {
  id: {
    type: Sequelize.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: Sequelize.BIGINT,
    allowNull: false
  },
  species: {
    type: Sequelize.STRING(64),
    defaultValue: 'succulent'
  },
  energy_value: {
    type: Sequelize.INTEGER,
    defaultValue: 0,
    comment: '能量值'
  },
  level: {
    type: Sequelize.INTEGER,
    defaultValue: 1
  },
  created_at: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW,
    comment: '创建时间'
  },
  updated_at: {
    type: Sequelize.DATE,
    allowNull: true,
    comment: '更新时间'
  }
}, {
  timestamps: false,
  tableName: 'plants'
})

export default Plant
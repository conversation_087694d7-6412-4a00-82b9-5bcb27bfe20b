import { DataTypes } from 'sequelize'
import sequelize from '../lib/sequelize'

const Admin = sequelize.define('Admin', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING(64),
    allowNull: false,
    unique: true,
    comment: '管理员用户名'
  },
  password: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '密码（加密后）'
  },
  real_name: {
    type: DataTypes.STRING(64),
    allowNull: true,
    comment: '真实姓名'
  },
  email: {
    type: DataTypes.STRING(128),
    allowNull: true,
    comment: '邮箱'
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '手机号'
  },
  role: {
    type: DataTypes.ENUM('super_admin', 'admin', 'editor'),
    defaultValue: 'editor',
    comment: '角色：超级管理员/管理员/编辑员'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive'),
    defaultValue: 'active',
    comment: '状态：激活/禁用'
  },
  last_login_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最后登录时间'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '更新时间'
  }
}, {
  tableName: 'admins',
  timestamps: false,
  comment: '管理员表'
})

export default Admin

# 冥想应用后台管理系统 API 文档

## 概述

本文档描述了冥想应用后台管理系统的所有API接口。后台管理系统提供了用户管理、内容管理、多肉养成系统管理等功能。

## 认证机制

### 管理员认证
- 所有管理端接口（除登录外）都需要管理员认证
- 使用JWT Token进行认证
- Token在请求头中传递：`Authorization: Bearer <token>`
- Token有效期：24小时

### 权限控制
系统采用基于角色的权限控制（RBAC）：

- **super_admin（超级管理员）**：拥有所有权限
- **admin（管理员）**：拥有大部分管理权限，不能管理其他管理员
- **editor（编辑员）**：只能管理内容相关功能

## 基础信息

- **Base URL**: `http://your-domain.com`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## API 接口列表

### 1. 管理员模块

#### 1.1 管理员登录
```
POST /admin/login
```

**请求参数：**
```json
{
  "username": "admin",
  "password": "123456"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "admin": {
      "id": 1,
      "username": "admin",
      "real_name": "超级管理员",
      "email": "<EMAIL>",
      "role": "super_admin"
    }
  }
}
```

#### 1.2 获取管理员信息
```
GET /admin/profile
```
**权限要求：** 管理员认证

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "id": 1,
    "username": "admin",
    "real_name": "超级管理员",
    "email": "<EMAIL>",
    "role": "super_admin",
    "status": "active",
    "last_login_at": "2025-08-18T10:30:00.000Z",
    "created_at": "2025-08-18T08:00:00.000Z"
  }
}
```

#### 1.3 获取管理员列表
```
GET /admin/admins?page=1&limit=10&search=关键词
```
**权限要求：** 超级管理员

**查询参数：**
- `page`: 页码（默认1）
- `limit`: 每页数量（默认10）
- `search`: 搜索关键词（可选）

#### 1.4 创建管理员
```
POST /admin/admins
```
**权限要求：** 超级管理员

**请求参数：**
```json
{
  "username": "newadmin",
  "password": "123456",
  "real_name": "新管理员",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "role": "admin"
}
```

#### 1.5 更新管理员信息
```
PUT /admin/admins/{id}
```
**权限要求：** 超级管理员

### 2. 用户管理模块

#### 2.1 获取用户列表
```
GET /admin/users?page=1&limit=10&search=关键词&meditation_level=2&start_date=2025-08-01&end_date=2025-08-18
```
**权限要求：** 超级管理员、管理员

**查询参数：**
- `page`: 页码
- `limit`: 每页数量
- `search`: 搜索关键词（昵称、openid）
- `meditation_level`: 冥想等级筛选
- `start_date`: 注册开始日期
- `end_date`: 注册结束日期

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": 1,
        "openid": "openid_test_1",
        "nickname": "欢欢",
        "avatar_url": "https://example.com/avatar.jpg",
        "meditation_level": 2,
        "streak_days": 5,
        "plant_count": 2,
        "created_at": "2025-08-10T08:00:00.000Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 10,
      "total": 100,
      "total_pages": 10
    }
  }
}
```

#### 2.2 获取用户详情
```
GET /admin/users/{id}
```
**权限要求：** 超级管理员、管理员

#### 2.3 更新用户等级
```
PUT /admin/users/{id}/level
```
**权限要求：** 超级管理员、管理员

**请求参数：**
```json
{
  "meditation_level": 3,
  "reason": "手动调整等级"
}
```

#### 2.4 获取用户统计数据
```
GET /admin/users/statistics
```
**权限要求：** 超级管理员、管理员

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "total_users": 1000,
    "level_distribution": [
      {"meditation_level": 1, "count": 500},
      {"meditation_level": 2, "count": 300},
      {"meditation_level": 3, "count": 200}
    ],
    "recent_registrations": [
      {"date": "2025-08-18", "count": 10},
      {"date": "2025-08-17", "count": 8}
    ],
    "active_users": 150
  }
}
```

### 3. 用户等级管理

#### 3.1 获取等级配置列表
```
GET /admin/user-levels
```
**权限要求：** 超级管理员、管理员

#### 3.2 创建等级配置
```
POST /admin/user-levels
```
**权限要求：** 超级管理员、管理员

**请求参数：**
```json
{
  "level": 6,
  "level_name": "宗师",
  "required_days": 60,
  "required_duration": 108000,
  "benefits": {
    "description": "坚持冥想60天",
    "features": ["解锁传说多肉", "能量获取+100%", "专属头像框"]
  },
  "icon_url": "https://example.com/level6.png"
}
```

#### 3.3 更新等级配置
```
PUT /admin/user-levels/{id}
```

#### 3.4 删除等级配置
```
DELETE /admin/user-levels/{id}
```

### 4. 冥想内容管理

#### 4.1 获取内容列表
```
GET /admin/meditation/contents?page=1&limit=10&search=关键词&type=meditation&sub_type=course&status=published
```
**权限要求：** 管理员认证

**查询参数：**
- `type`: 内容类型（meditation/sleep/sound）
- `sub_type`: 子类型（course/single）
- `status`: 状态（draft/published/archived）

#### 4.2 创建冥想内容
```
POST /admin/meditation/contents
```
**权限要求：** 超级管理员、管理员、编辑员

**请求参数：**
```json
{
  "title": "正念呼吸冥想",
  "description": "通过专注呼吸来培养正念",
  "type": "meditation",
  "sub_type": "single",
  "parent_id": null,
  "cover_url": "https://example.com/cover.jpg",
  "audio_url": "https://example.com/audio.mp3",
  "video_url": "https://example.com/video.mp4",
  "duration": 1200,
  "tag_ids": [1, 2, 3],
  "status": "published"
}
```

#### 4.3 获取内容详情
```
GET /admin/meditation/contents/{id}
```

#### 4.4 更新内容
```
PUT /admin/meditation/contents/{id}
```

#### 4.5 删除内容
```
DELETE /admin/meditation/contents/{id}
```
**权限要求：** 超级管理员、管理员

### 5. 标签管理

#### 5.1 获取标签列表
```
GET /admin/meditation/tags?page=1&limit=10&search=关键词
```

#### 5.2 创建标签
```
POST /admin/meditation/tags
```
**权限要求：** 超级管理员、管理员、编辑员

**请求参数：**
```json
{
  "name": "深度放松"
}
```

#### 5.3 更新标签
```
PUT /admin/meditation/tags/{id}
```

#### 5.4 删除标签
```
DELETE /admin/meditation/tags/{id}
```

#### 5.5 批量删除标签
```
DELETE /admin/meditation/tags/batch-delete
```

**请求参数：**
```json
{
  "tag_ids": [1, 2, 3]
}
```

### 6. 多肉品种管理

#### 6.1 获取品种列表
```
GET /admin/plants/species?page=1&limit=10&search=关键词&rarity=rare
```
**权限要求：** 超级管理员、管理员

#### 6.2 创建多肉品种
```
POST /admin/plants/species
```
**权限要求：** 超级管理员、管理员

**请求参数：**
```json
{
  "name": "rainbow_succulent",
  "display_name": "彩虹多肉",
  "description": "拥有彩虹般色彩的神奇多肉",
  "rarity": "legendary",
  "unlock_condition": {
    "level": 5,
    "days": 30,
    "special_achievement": "rainbow_master"
  },
  "growth_stages": {
    "stages": [
      {"level": 1, "name": "彩虹种子"},
      {"level": 15, "name": "彩虹幼苗"},
      {"level": 30, "name": "彩虹之花"}
    ]
  },
  "max_level": 30,
  "base_energy_per_level": 200,
  "image_urls": {
    "1": "/images/plants/rainbow_1.png",
    "15": "/images/plants/rainbow_15.png",
    "30": "/images/plants/rainbow_30.png"
  }
}
```

#### 6.3 更新品种配置
```
PUT /admin/plants/species/{id}
```

### 7. 能量奖励管理

#### 7.1 获取奖励规则列表
```
GET /admin/plants/energy-rules
```
**权限要求：** 超级管理员、管理员

#### 7.2 创建奖励规则
```
POST /admin/plants/energy-rules
```
**权限要求：** 超级管理员、管理员

**请求参数：**
```json
{
  "rule_name": "完成长时间冥想",
  "rule_type": "meditation_complete",
  "condition": {
    "min_duration": 3600,
    "meditation_type": "meditation"
  },
  "energy_amount": 100,
  "bonus_multiplier": 1.5,
  "max_daily_times": 1,
  "description": "完成1小时冥想获得额外奖励",
  "priority": 10
}
```

### 8. 统计数据

#### 8.1 获取多肉统计数据
```
GET /admin/plants/statistics
```
**权限要求：** 超级管理员、管理员

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "total_plants": 5000,
    "level_distribution": [
      {"level": 1, "count": 2000},
      {"level": 5, "count": 1500},
      {"level": 10, "count": 1000}
    ],
    "species_distribution": [
      {"species": "succulent", "count": 3000},
      {"species": "rare_succulent", "count": 1500}
    ],
    "recent_growth_records": [
      {"date": "2025-08-18", "count": 100, "total_energy": 5000},
      {"date": "2025-08-17", "count": 95, "total_energy": 4800}
    ],
    "active_plant_users": 200
  }
}
```

### 9. 文件上传

#### 9.1 文件上传
```
POST /admin/upload
```
**权限要求：** 管理员认证

**请求格式：** multipart/form-data

**响应示例：**
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "url": "https://example.com/uploads/image.jpg",
    "filename": "image.jpg",
    "size": 102400
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 数据库变更

请执行 `admin_system_database_changes.sql` 文件来创建后台管理系统所需的数据表和字段。

## 部署说明

1. 执行数据库变更脚本
2. 安装依赖：`npm install bcrypt`
3. 重启应用服务
4. 使用默认管理员账户登录：
   - 用户名：admin
   - 密码：admin123

## 注意事项

1. 所有敏感操作都有权限控制
2. 管理员密码使用bcrypt加密存储
3. JWT Token需要定期刷新
4. 建议在生产环境中修改默认管理员密码
5. 文件上传需要配置适当的存储路径和权限

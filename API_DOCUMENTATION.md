# 微信小程序冥想应用后端API文档

## 基础信息
- 基础URL: `http://localhost:3000/api`
- 认证方式: JWT Token (除登录接口外都需要在Header中携带 `Authorization: Bearer <token>`)

## 用户模块

### 1. 微信code换取openid
```
POST /public/user/code2session
```

**请求参数:**
```json
{
  "code": "微信小程序登录时获取的code"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "获取openid成功",
  "data": {
    "openid": "openid_test_123",
    "session_key": "session_key_123",
    "unionid": "unionid_test_123"
  }
}
```

### 2. 微信小程序用户登录
```
POST /public/user/login
```

**请求参数:**
```json
{
  "openid": "微信openid",
  "unionid": "微信unionid (可选)",
  "nickname": "用户昵称 (可选)",
  "avatar_url": "头像URL (可选)"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "JWT token",
    "user": {
      "id": 1,
      "openid": "openid_test_1",
      "nickname": "欢欢",
      "avatar_url": "头像URL",
      "meditation_level": 2,
      "streak_days": 5
    }
  }
}
```

### 3. 获取用户基础信息
```
GET /user/profile
```

**响应:**
```json
{
  "code": 200,
  "data": {
    "meditation_level": 2,
    "streak_days": 5,
    "plant_count": 2
  }
}
```

### 3. 获取用户收藏的冥想内容
```
GET /user/favorites?page=1&limit=10
```

**响应:**
```json
{
  "code": 200,
  "data": {
    "total": 5,
    "page": 1,
    "limit": 10,
    "items": [...]
  }
}
```

### 4. 获取冥想统计数据
```
GET /user/meditation-stats?period_type=day&start_date=2024-01-01&end_date=2024-01-31
```

**参数:**
- `period_type`: day/week/month/year
- `start_date`: 开始日期 (可选)
- `end_date`: 结束日期 (可选)

## 冥想内容模块

### 1. 获取冥想内容列表
```
GET /meditation/list?type=meditation&page=1&limit=10
```

**参数:**
- `type`: meditation/sleep/sound (可选)
- `sub_type`: course/single (可选)
- `parent_id`: 父课程ID (可选)
- `search`: 搜索关键词 (可选)
- `page`: 页码
- `limit`: 每页数量

### 2. 获取冥想内容详情
```
GET /meditation/:id
```

### 3. 收藏/取消收藏冥想内容
```
POST /meditation/:id/favorite
```

### 4. 搜索冥想内容
```
GET /meditation/search?keyword=正念&type=meditation&page=1&limit=10
```

### 5. 获取标签列表
```
GET /meditation/tags
```

## 计划模块

### 1. 获取每日计划（包括前后三天）
```
GET /plan/daily?date=2024-01-15
```

### 2. 添加冥想内容到计划
```
POST /plan/add
```

**请求参数:**
```json
{
  "meditation_id": 1,
  "plan_date": "2024-01-15"
}
```

### 3. 从计划中删除冥想内容
```
DELETE /plan/item/:id
```

### 4. 完成计划项
```
PUT /plan/item/:id/complete
```

### 5. 获取历史计划
```
GET /plan/history?page=1&limit=10&start_date=2024-01-01&end_date=2024-01-31
```

### 6. 获取计划统计
```
GET /plan/stats?start_date=2024-01-01&end_date=2024-01-31
```

## 多肉模块

### 1. 获取多肉列表
```
GET /plant/list
```

### 2. 获取多肉详情
```
GET /plant/:id
```

### 3. 为多肉添加能量
```
POST /plant/:id/energy
```

**请求参数:**
```json
{
  "energy_value": 10,
  "reason": "完成冥想任务"
}
```

### 4. 创建新多肉
```
POST /plant/create
```

**请求参数:**
```json
{
  "species": "succulent"
}
```

### 5. 获取多肉成长记录
```
GET /plant/:id/records?page=1&limit=20
```

## 错误码说明

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权（token无效或过期）
- `404`: 资源不存在
- `500`: 服务器内部错误

## 使用示例

### 1. 用户登录流程
```javascript
// 1. 微信小程序获取用户信息后调用登录接口
const loginRes = await fetch('/api/public/user/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    openid: 'wx_openid_123',
    nickname: '用户昵称',
    avatar_url: 'https://avatar.url'
  })
})

// 2. 保存token用于后续请求
const { token } = loginRes.data
localStorage.setItem('token', token)

// 3. 后续请求携带token
const profileRes = await fetch('/api/user/profile', {
  headers: { 'Authorization': `Bearer ${token}` }
})
```

### 2. 冥想内容搜索
```javascript
const searchRes = await fetch('/api/meditation/search?keyword=正念&page=1&limit=10', {
  headers: { 'Authorization': `Bearer ${token}` }
})
```

### 3. 计划管理
```javascript
// 添加到计划
await fetch('/api/plan/add', {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}` 
  },
  body: JSON.stringify({
    meditation_id: 1,
    plan_date: '2024-01-15'
  })
})

// 完成计划项
await fetch('/api/plan/item/1/complete', {
  method: 'PUT',
  headers: { 'Authorization': `Bearer ${token}` }
})
```
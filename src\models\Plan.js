import Sequelize from 'sequelize'
import sequelize from '../lib/sequelize'

const Plan = sequelize.define('plans', {
  id: {
    type: Sequelize.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: Sequelize.BIGINT,
    allowNull: false
  },
  plan_date: {
    type: Sequelize.DATEONLY,
    allowNull: false,
    comment: '计划日期'
  },
  created_at: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW
  }
}, {
  timestamps: false,
  tableName: 'plans'
})

export default Plan
import Sequelize from 'sequelize'
import sequelize from '../lib/sequelize'

const MeditationTag = sequelize.define('meditation_tags', {
  id: {
    type: Sequelize.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: Sequelize.STRING(64),
    allowNull: false,
    unique: true
  },
  created_at: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW
  }
}, {
  timestamps: false,
  tableName: 'meditation_tags'
})

export default MeditationTag
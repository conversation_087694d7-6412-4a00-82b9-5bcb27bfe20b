import MeditationContent from '../models/MeditationContent'
import MeditationTag from '../models/MeditationTag'
import MeditationContentTag from '../models/MeditationContentTag'
import { Op } from 'sequelize'
import { parsePaginationParams, formatPaginationResponse } from '../tool/Common'

export default class AdminMeditationController {
  /**
   * @swagger
   * /admin/meditation/contents:
   *   get:
   *     tags:
   *       - 冥想内容管理
   *     summary: 获取冥想内容列表
   *     description: 获取所有冥想内容列表，支持搜索和筛选
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: 搜索关键词
   *       - in: query
   *         name: type
   *         schema:
   *           type: string
   *           enum: [meditation, sleep, sound]
   *         description: 内容类型
   *       - in: query
   *         name: sub_type
   *         schema:
   *           type: string
   *           enum: [course, single]
   *         description: 子类型
   *       - in: query
   *         name: status
   *         schema:
   *           type: string
   *           enum: [draft, published, archived]
   *         description: 状态
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getContentList(ctx) {
    const { search, type, sub_type, status } = ctx.query

    try {
      const { pageNum, pageSize, offset } = parsePaginationParams(ctx.query)
      const whereCondition = {}

      // 搜索条件
      if (search) {
        whereCondition[Op.or] = [
          { title: { [Op.like]: `%${search}%` } },
          { description: { [Op.like]: `%${search}%` } },
          { tags_text: { [Op.like]: `%${search}%` } }
        ]
      }

      // 类型筛选
      if (type) whereCondition.type = type
      if (sub_type) whereCondition.sub_type = sub_type
      if (status) whereCondition.status = status

      const contents = await MeditationContent.findAndCountAll({
        where: whereCondition,
        limit: pageSize,
        offset: offset,
        order: [['created_at', 'DESC']],
        include: [
          {
            model: MeditationContent,
            as: 'parent',
            attributes: ['id', 'title']
          },
          {
            model: MeditationTag,
            as: 'tags',
            through: { attributes: [] }
          }
        ]
      })

      ctx.body = {
        code: 200,
        data: formatPaginationResponse(contents.rows, contents.count, pageNum, pageSize)
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取内容列表失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/meditation/contents:
   *   post:
   *     tags:
   *       - 冥想内容管理
   *     summary: 创建冥想内容
   *     description: 创建新的冥想内容
   *     security:
   *       - AdminBearer: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - title
   *               - type
   *             properties:
   *               title:
   *                 type: string
   *                 description: 标题
   *               description:
   *                 type: string
   *                 description: 描述
   *               type:
   *                 type: string
   *                 enum: [meditation, sleep, sound]
   *                 description: 类型
   *               sub_type:
   *                 type: string
   *                 enum: [course, single]
   *                 description: 子类型
   *               parent_id:
   *                 type: integer
   *                 description: 父课程ID
   *               cover_url:
   *                 type: string
   *                 description: 封面图片URL
   *               audio_url:
   *                 type: string
   *                 description: 音频文件URL
   *               video_url:
   *                 type: string
   *                 description: 视频文件URL
   *               duration:
   *                 type: integer
   *                 description: 时长（秒）
   *               tag_ids:
   *                 type: array
   *                 items:
   *                   type: integer
   *                 description: 标签ID数组
   *               status:
   *                 type: string
   *                 enum: [draft, published, archived]
   *                 description: 状态
   *     responses:
   *       200:
   *         description: 创建成功
   */
  static async createContent(ctx) {
    const { 
      title, description, type, sub_type, parent_id, 
      cover_url, audio_url, video_url, duration, tag_ids, status 
    } = ctx.request.body

    if (!title || !type) {
      ctx.body = {
        code: 400,
        message: '标题和类型不能为空'
      }
      return
    }

    try {
      // 创建内容
      const content = await MeditationContent.create({
        title,
        description,
        type,
        sub_type,
        parent_id,
        cover_url,
        audio_url,
        video_url,
        duration: duration || 0,
        status: status || 'draft'
      })

      // 关联标签
      if (tag_ids && tag_ids.length > 0) {
        const tagAssociations = tag_ids.map(tagId => ({
          meditation_id: content.id,
          tag_id: tagId
        }))
        await MeditationContentTag.bulkCreate(tagAssociations)

        // 更新标签文本（用于搜索）
        const tags = await MeditationTag.findAll({
          where: { id: { [Op.in]: tag_ids } }
        })
        const tagsText = tags.map(tag => tag.name).join(',')
        await content.update({ tags_text: tagsText })
      }

      ctx.body = {
        code: 200,
        message: '内容创建成功',
        data: content
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '创建内容失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/meditation/contents/{id}:
   *   get:
   *     tags:
   *       - 冥想内容管理
   *     summary: 获取冥想内容详情
   *     description: 获取指定冥想内容的详细信息
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 内容ID
   *     responses:
   *       200:
   *         description: 获取成功
   *       404:
   *         description: 内容不存在
   */
  static async getContentDetail(ctx) {
    const { id } = ctx.params

    try {
      const content = await MeditationContent.findByPk(id, {
        include: [
          {
            model: MeditationContent,
            as: 'parent',
            attributes: ['id', 'title']
          },
          {
            model: MeditationTag,
            as: 'tags',
            through: { attributes: [] }
          }
        ]
      })

      if (!content) {
        ctx.body = {
          code: 404,
          message: '内容不存在'
        }
        return
      }

      ctx.body = {
        code: 200,
        data: content
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取内容详情失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/meditation/contents/{id}:
   *   put:
   *     tags:
   *       - 冥想内容管理
   *     summary: 更新冥想内容
   *     description: 更新指定的冥想内容
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 内容ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               title:
   *                 type: string
   *               description:
   *                 type: string
   *               cover_url:
   *                 type: string
   *               audio_url:
   *                 type: string
   *               video_url:
   *                 type: string
   *               duration:
   *                 type: integer
   *               tag_ids:
   *                 type: array
   *                 items:
   *                   type: integer
   *               status:
   *                 type: string
   *                 enum: [draft, published, archived]
   *     responses:
   *       200:
   *         description: 更新成功
   *       404:
   *         description: 内容不存在
   */
  static async updateContent(ctx) {
    const { id } = ctx.params
    const {
      title, description, cover_url, audio_url, video_url,
      duration, tag_ids, status
    } = ctx.request.body

    try {
      const content = await MeditationContent.findByPk(id)
      if (!content) {
        ctx.body = {
          code: 404,
          message: '内容不存在'
        }
        return
      }

      // 更新基本信息
      const updateData = {}
      if (title !== undefined) updateData.title = title
      if (description !== undefined) updateData.description = description
      if (cover_url !== undefined) updateData.cover_url = cover_url
      if (audio_url !== undefined) updateData.audio_url = audio_url
      if (video_url !== undefined) updateData.video_url = video_url
      if (duration !== undefined) updateData.duration = duration
      if (status !== undefined) updateData.status = status

      await content.update(updateData)

      // 更新标签关联
      if (tag_ids !== undefined) {
        // 删除原有标签关联
        await MeditationContentTag.destroy({
          where: { meditation_id: id }
        })

        // 创建新的标签关联
        if (tag_ids.length > 0) {
          const tagAssociations = tag_ids.map(tagId => ({
            meditation_id: id,
            tag_id: tagId
          }))
          await MeditationContentTag.bulkCreate(tagAssociations)

          // 更新标签文本
          const tags = await MeditationTag.findAll({
            where: { id: { [Op.in]: tag_ids } }
          })
          const tagsText = tags.map(tag => tag.name).join(',')
          await content.update({ tags_text: tagsText })
        } else {
          await content.update({ tags_text: null })
        }
      }

      ctx.body = {
        code: 200,
        message: '内容更新成功',
        data: content
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '更新内容失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/meditation/contents/{id}:
   *   delete:
   *     tags:
   *       - 冥想内容管理
   *     summary: 删除冥想内容
   *     description: 删除指定的冥想内容
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 内容ID
   *     responses:
   *       200:
   *         description: 删除成功
   *       404:
   *         description: 内容不存在
   */
  static async deleteContent(ctx) {
    const { id } = ctx.params

    try {
      const content = await MeditationContent.findByPk(id)
      if (!content) {
        ctx.body = {
          code: 404,
          message: '内容不存在'
        }
        return
      }

      // 检查是否有子内容
      const childCount = await MeditationContent.count({
        where: { parent_id: id }
      })

      if (childCount > 0) {
        ctx.body = {
          code: 400,
          message: '该内容下还有子内容，无法删除'
        }
        return
      }

      await content.destroy()

      ctx.body = {
        code: 200,
        message: '内容删除成功'
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '删除内容失败',
        error: error.message
      }
    }
  }
}

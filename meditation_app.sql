-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS meditation_app DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE meditation_app;

-- =========================
-- 1. 用户表
-- =========================
DROP TABLE IF EXISTS users;
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    openid VARCHAR(64) NOT NULL UNIQUE COMMENT '微信openid',
    unionid VARCHAR(64) DEFAULT NULL COMMENT '微信unionid',
    nickname VARCHAR(128) DEFAULT NULL COMMENT '昵称',
    avatar_url VARCHAR(512) DEFAULT NULL COMMENT '头像URL',
    meditation_level INT DEFAULT 1 COMMENT '冥想等级',
    streak_days INT DEFAULT 0 COMMENT '连续坚持天数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 触发器：更新时自动写入 updated_at
DELIMITER $$
CREATE TRIGGER trg_users_updated_at
BEFORE UPDATE ON users
FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- =========================
-- 2. 冥想内容表
-- =========================
DROP TABLE IF EXISTS meditation_content;
CREATE TABLE meditation_content (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    type ENUM('meditation','sleep','sound') NOT NULL COMMENT '类型：冥想/睡眠/声音',
    sub_type ENUM('course','single') DEFAULT NULL COMMENT '冥想子类型：课程/单节',
    parent_id BIGINT DEFAULT NULL COMMENT '父课程ID',
    title VARCHAR(255) NOT NULL COMMENT '标题',
    description TEXT COMMENT '简介',
    cover_url VARCHAR(512) DEFAULT NULL COMMENT '封面图片URL',
    duration INT DEFAULT 0 COMMENT '时长（秒）',
    tags_text VARCHAR(255) DEFAULT NULL COMMENT '冗余标签文本，方便搜索',
    favorite_count INT DEFAULT 0 COMMENT '收藏人数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NULL COMMENT '更新时间',
    FOREIGN KEY (parent_id) REFERENCES meditation_content(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='冥想内容';

DELIMITER $$
CREATE TRIGGER trg_meditation_content_updated_at
BEFORE UPDATE ON meditation_content
FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- =========================
-- 3. 用户收藏表
-- =========================
DROP TABLE IF EXISTS user_favorites;
CREATE TABLE user_favorites (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    meditation_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uniq_user_fav (user_id, meditation_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (meditation_id) REFERENCES meditation_content(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏的冥想内容';

-- =========================
-- 4. 用户冥想统计
-- =========================
DROP TABLE IF EXISTS user_meditation_stats;
CREATE TABLE user_meditation_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    period_type ENUM('day','week','month','year') NOT NULL,
    period_date DATE NOT NULL COMMENT '周期开始日期',
    meditation_duration INT DEFAULT 0 COMMENT '冥想时长(秒)',
    energy_gained INT DEFAULT 0 COMMENT '获取能量值',
    tasks_completed INT DEFAULT 0 COMMENT '完成任务数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uniq_period (user_id, period_type, period_date),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户冥想统计';

-- =========================
-- 5. 用户坚持记录
-- =========================
DROP TABLE IF EXISTS user_streaks;
CREATE TABLE user_streaks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    streak_start DATE NOT NULL,
    streak_end DATE NOT NULL,
    total_days INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户坚持记录';

-- =========================
-- 6. 标签表
-- =========================
DROP TABLE IF EXISTS meditation_tags;
CREATE TABLE meditation_tags (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(64) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='冥想标签';

-- =========================
-- 7. 内容标签关联表
-- =========================
DROP TABLE IF EXISTS meditation_content_tags;
CREATE TABLE meditation_content_tags (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    meditation_id BIGINT NOT NULL,
    tag_id BIGINT NOT NULL,
    FOREIGN KEY (meditation_id) REFERENCES meditation_content(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES meditation_tags(id) ON DELETE CASCADE,
    UNIQUE KEY uniq_content_tag (meditation_id, tag_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='冥想内容与标签关联';

-- =========================
-- 8. 计划表
-- =========================
DROP TABLE IF EXISTS plans;
CREATE TABLE plans (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    plan_date DATE NOT NULL COMMENT '计划日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uniq_user_date (user_id, plan_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户每日计划';

-- =========================
-- 9. 计划项表
-- =========================
DROP TABLE IF EXISTS plan_items;
CREATE TABLE plan_items (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    plan_id BIGINT NOT NULL,
    meditation_id BIGINT NOT NULL,
    seq INT DEFAULT 0 COMMENT '顺序',
    completed TINYINT(1) DEFAULT 0 COMMENT '是否完成',
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (plan_id) REFERENCES plans(id) ON DELETE CASCADE,
    FOREIGN KEY (meditation_id) REFERENCES meditation_content(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计划项';

-- =========================
-- 10. 计划历史记录
-- =========================
DROP TABLE IF EXISTS plan_history;
CREATE TABLE plan_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    meditation_id BIGINT NOT NULL,
    completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (meditation_id) REFERENCES meditation_content(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计划历史记录';

-- =========================
-- 11. 多肉表
-- =========================
DROP TABLE IF EXISTS plants;
CREATE TABLE plants (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    species VARCHAR(64) DEFAULT 'succulent',
    energy_value INT DEFAULT 0 COMMENT '能量值',
    level INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NULL COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户多肉';

DELIMITER $$
CREATE TRIGGER trg_plants_updated_at
BEFORE UPDATE ON plants
FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- =========================
-- 12. 多肉成长记录
-- =========================
DROP TABLE IF EXISTS plant_growth_records;
CREATE TABLE plant_growth_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    plant_id BIGINT NOT NULL,
    change_value INT NOT NULL COMMENT '能量值变化',
    reason VARCHAR(255) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (plant_id) REFERENCES plants(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='多肉成长记录';

-- =========================
-- 初始化测试数据
-- =========================
INSERT INTO users (openid, nickname, meditation_level, streak_days) VALUES
('openid_test_1', '欢欢', 2, 5),
('openid_test_2', '喵喵', 1, 1);

INSERT INTO meditation_content (type, sub_type, title, description, duration, tags_text) VALUES
('meditation', 'course', '正念冥想系列', '提升专注力的正念冥想', 1800, '正念,专注'),
('meditation', 'single', '正念冥想第一课', '正念入门', 600, '正念,专注'),
('sleep', NULL, '深度助眠音频', '让你快速入睡的音乐', 900, '睡眠,放松');

INSERT INTO meditation_tags (name) VALUES ('正念'), ('专注'), ('睡眠'), ('放松');

INSERT INTO meditation_content_tags (meditation_id, tag_id) VALUES
(1, 1), (1, 2), (2, 1), (3, 3), (3, 4);

INSERT INTO plants (user_id, species, energy_value, level) VALUES
(1, 'succulent', 50, 1),
(1, 'succulent', 120, 2),
(2, 'succulent', 30, 1);

INSERT INTO plant_growth_records (plant_id, change_value, reason) VALUES
(1, 10, '完成冥想任务'),
(1, 40, '连续冥想奖励'),
(2, 20, '完成睡眠音频任务');

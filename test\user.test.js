import request from 'supertest'
import app from '../src/app'

describe('用户模块测试', () => {
  describe('POST /api/public/user/code2session', () => {
    it('应该返回400错误当code为空时', async () => {
      const response = await request(app)
        .post('/api/public/user/code2session')
        .send({})

      expect(response.status).toBe(200)
      expect(response.body.code).toBe(400)
      expect(response.body.message).toBe('code不能为空')
    })

    it('应该返回500错误当code无效时', async () => {
      const response = await request(app)
        .post('/api/public/user/code2session')
        .send({
          code: 'invalid_code'
        })

      expect(response.status).toBe(200)
      expect(response.body.code).toBe(500)
      expect(response.body.message).toBe('获取openid失败')
    })
  })

  describe('POST /api/public/user/login', () => {
    it('应该返回400错误当openid为空时', async () => {
      const response = await request(app)
        .post('/api/public/user/login')
        .send({})

      expect(response.status).toBe(200)
      expect(response.body.code).toBe(400)
      expect(response.body.message).toBe('openid不能为空')
    })

    it('应该成功登录当提供有效openid时', async () => {
      const response = await request(app)
        .post('/api/public/user/login')
        .send({
          openid: 'test_openid_123',
          nickname: '测试用户',
          avatar_url: 'https://example.com/avatar.jpg'
        })

      expect(response.status).toBe(200)
      expect(response.body.code).toBe(200)
      expect(response.body.message).toBe('登录成功')
      expect(response.body.data).toHaveProperty('token')
      expect(response.body.data).toHaveProperty('user')
    })
  })
})

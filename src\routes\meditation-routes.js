import KoaRouter from 'koa-router'
import controllers from '../controllers'

const router = new KoaRouter()

export default router
  // 微信小程序用户相关路由
  .post('/public/user/code2session', controllers.user.code2Session) // 微信code换取openid
  .post('/public/user/login', controllers.user.login) // 微信登录
  .get('/user/profile', controllers.user.getProfile) // 获取用户基础信息
  .get('/user/favorites', controllers.user.getFavorites) // 获取收藏列表
  .get('/user/plants', controllers.user.getPlants) // 获取用户多肉
  .get('/user/meditation-stats', controllers.user.getMeditationStats) // 获取冥想统计

  // 冥想内容相关路由
  .get('/meditation/list', controllers.meditation.getList) // 获取冥想内容列表
  .get('/meditation/:id', controllers.meditation.getDetail) // 获取冥想内容详情
  .post('/meditation/:id/favorite', controllers.meditation.toggleFavorite) // 收藏/取消收藏
  .get('/meditation/search', controllers.meditation.search) // 搜索冥想内容
  .get('/meditation/tags', controllers.meditation.getTags) // 获取标签

  // 计划相关路由
  .get('/plan/daily', controllers.plan.getDailyPlans) // 获取每日计划
  .post('/plan/add', controllers.plan.addToPlan) // 添加到计划
  .delete('/plan/item/:id', controllers.plan.removeFromPlan) // 从计划删除
  .put('/plan/item/:id/complete', controllers.plan.completePlanItem) // 完成计划项
  .get('/plan/history', controllers.plan.getPlanHistory) // 获取历史计划
  .get('/plan/stats', controllers.plan.getPlanStats) // 获取计划统计

  // 多肉相关路由
  .get('/plant/list', controllers.plant.getPlants) // 获取多肉列表
  .get('/plant/:id', controllers.plant.getPlantDetail) // 获取多肉详情
  .post('/plant/:id/energy', controllers.plant.addEnergy) // 添加能量
  .post('/plant/create', controllers.plant.createPlant) // 创建新多肉
  .get('/plant/:id/records', controllers.plant.getGrowthRecords) // 获取成长记录

# 后台管理系统部署指南

## 概述

本指南将帮助您部署和配置冥想应用的后台管理系统。

## 系统要求

- Node.js 14.0+
- MySQL 5.7+
- 现有的冥想应用项目

## 部署步骤

### 1. 安装依赖

```bash
npm install bcrypt
```

### 2. 执行数据库变更

执行项目根目录下的 `admin_system_database_changes.sql` 文件：

```bash
mysql -u your_username -p meditation_app < admin_system_database_changes.sql
```

或者在MySQL客户端中执行：

```sql
source /path/to/admin_system_database_changes.sql;
```

### 3. 更新模型关联

需要更新 `src/models/associations.js` 文件，添加新模型的关联关系：

```javascript
// 在文件末尾添加以下关联
import Admin from './Admin'
import UserLevelConfig from './UserLevelConfig'
import PlantSpecies from './PlantSpecies'
import EnergyRewardRule from './EnergyRewardRule'

// 多肉品种关联
Plant.belongsTo(PlantSpecies, { foreignKey: 'species_id', as: 'speciesInfo' })
PlantSpecies.hasMany(Plant, { foreignKey: 'species_id', as: 'plants' })

// 标签关联（如果还没有的话）
MeditationContent.belongsToMany(MeditationTag, { 
  through: MeditationContentTag, 
  foreignKey: 'meditation_id',
  as: 'tags'
})
MeditationTag.belongsToMany(MeditationContent, { 
  through: MeditationContentTag, 
  foreignKey: 'tag_id',
  as: 'contents'
})
MeditationTag.hasMany(MeditationContentTag, { foreignKey: 'tag_id', as: 'contentTags' })
```

### 4. 更新模型导出

在 `src/models/index.js` 中添加新模型的导出：

```javascript
import Admin from './Admin'
import UserLevelConfig from './UserLevelConfig'
import PlantSpecies from './PlantSpecies'
import EnergyRewardRule from './EnergyRewardRule'

export {
  // ... 现有导出
  Admin,
  UserLevelConfig,
  PlantSpecies,
  EnergyRewardRule
}
```

### 5. 重启应用

```bash
npm restart
# 或者
pm2 restart your-app-name
```

## 默认管理员账户

系统会自动创建一个默认的超级管理员账户：

- **用户名**: admin
- **密码**: admin123
- **角色**: super_admin

**重要**: 请在首次登录后立即修改默认密码！

## API 测试

### 1. 管理员登录测试

```bash
curl -X POST http://localhost:3000/admin/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

### 2. 获取用户列表测试

```bash
curl -X GET "http://localhost:3000/admin/users?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 权限说明

### 角色权限矩阵

| 功能模块 | super_admin | admin | editor |
|----------|-------------|-------|--------|
| 管理员管理 | ✅ | ❌ | ❌ |
| 用户管理 | ✅ | ✅ | ❌ |
| 用户等级管理 | ✅ | ✅ | ❌ |
| 内容管理 | ✅ | ✅ | ✅ |
| 标签管理 | ✅ | ✅ | ✅ |
| 多肉品种管理 | ✅ | ✅ | ❌ |
| 能量奖励管理 | ✅ | ✅ | ❌ |
| 统计数据查看 | ✅ | ✅ | ❌ |

### 创建新管理员

只有超级管理员可以创建新的管理员账户：

```bash
curl -X POST http://localhost:3000/admin/admins \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_SUPER_ADMIN_TOKEN" \
  -d '{
    "username": "content_editor",
    "password": "secure_password",
    "real_name": "内容编辑员",
    "email": "<EMAIL>",
    "role": "editor"
  }'
```

## 安全建议

### 1. 密码安全
- 使用强密码（至少8位，包含大小写字母、数字和特殊字符）
- 定期更换密码
- 不要在代码中硬编码密码

### 2. Token 安全
- JWT Token 有效期设置为24小时
- 在生产环境中使用HTTPS
- 定期轮换JWT密钥

### 3. 权限控制
- 遵循最小权限原则
- 定期审查管理员权限
- 及时删除不再需要的管理员账户

### 4. 操作日志
- 记录所有敏感操作
- 定期备份日志文件
- 监控异常操作

## 常见问题

### Q1: 忘记管理员密码怎么办？

可以直接在数据库中重置密码：

```sql
-- 生成新密码的哈希值（使用bcrypt）
-- 然后更新数据库
UPDATE admins SET password = '$2b$10$NEW_HASHED_PASSWORD' WHERE username = 'admin';
```

### Q2: 如何备份管理员数据？

```bash
mysqldump -u username -p meditation_app admins user_level_configs plant_species energy_reward_rules > admin_backup.sql
```

### Q3: 如何监控API使用情况？

建议在中间件中添加日志记录：

```javascript
// 在 adminAuth 中间件中添加
console.log(`Admin ${admin.username} accessed ${ctx.path} at ${new Date()}`)
```

### Q4: 如何扩展权限系统？

可以创建更细粒度的权限表：

```sql
CREATE TABLE admin_permissions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    admin_id BIGINT NOT NULL,
    permission_name VARCHAR(64) NOT NULL,
    granted BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (admin_id) REFERENCES admins(id)
);
```

## 维护建议

### 定期维护任务

1. **每周**：
   - 检查管理员登录日志
   - 清理过期的JWT Token
   - 备份管理员数据

2. **每月**：
   - 审查管理员权限
   - 更新系统依赖
   - 性能优化检查

3. **每季度**：
   - 安全审计
   - 密码策略检查
   - 系统升级评估

### 监控指标

- 管理员登录频率
- API 调用次数
- 错误率统计
- 响应时间监控

## 技术支持

如果在部署过程中遇到问题，请检查：

1. 数据库连接是否正常
2. 所有依赖是否正确安装
3. 文件权限是否正确设置
4. 端口是否被占用

更多技术细节请参考 `ADMIN_API_DOCUMENTATION.md` 文件。

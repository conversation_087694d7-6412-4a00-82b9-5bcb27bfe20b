-- 简单测试插入脚本，用于验证外键约束
USE meditation_app;

-- 清空数据
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE plant_growth_records;
TRUNCATE TABLE plants;
TRUNCATE TABLE plan_history;
TRUNCATE TABLE plan_items;
TRUNCATE TABLE plans;
TRUNCATE TABLE meditation_content_tags;
TRUNCATE TABLE meditation_tags;
TRUNCATE TABLE user_streaks;
TRUNCATE TABLE user_meditation_stats;
TRUNCATE TABLE user_favorites;
TRUNCATE TABLE meditation_content;
TRUNCATE TABLE users;
SET FOREIGN_KEY_CHECKS = 1;

-- 插入测试用户
INSERT INTO users (openid, unionid, nickname, avatar_url, meditation_level, streak_days) VALUES
('openid_test_123', 'unionid_test_123', '测试用户', 'https://youke1.picui.cn/s1/2025/08/11/68999a8cbe3d0.png', 1, 0);

-- 插入测试标签
INSERT INTO meditation_tags (name) VALUES ('正念'), ('专注'), ('睡眠');

-- 插入测试冥想内容
INSERT INTO meditation_content (type, sub_type, parent_id, title, description, cover_url, duration, tags_text, favorite_count) VALUES
('meditation', 'single', NULL, '正念冥想练习', '基础正念冥想练习', 'https://example.com/cover.jpg', 600, '正念,专注', 10);

-- 插入内容标签关联
INSERT INTO meditation_content_tags (meditation_id, tag_id) VALUES (1, 1), (1, 2);

-- 插入用户收藏
INSERT INTO user_favorites (user_id, meditation_id) VALUES (1, 1);

-- 插入多肉
INSERT INTO plants (user_id, species, energy_value, level) VALUES (1, '仙人掌', 0, 1);

-- 插入多肉成长记录
INSERT INTO plant_growth_records (plant_id, change_value, reason) VALUES (1, 10, '完成冥想');

-- 插入计划
INSERT INTO plans (user_id, plan_date) VALUES (1, '2025-08-11');

-- 插入计划项
INSERT INTO plan_items (plan_id, meditation_id, seq, completed, completed_at) VALUES (1, 1, 1, 1, '2025-08-11 08:00:00');

-- 插入计划历史
INSERT INTO plan_history (user_id, meditation_id, completed_at) VALUES (1, 1, '2025-08-11 08:00:00');

-- 插入用户统计
INSERT INTO user_meditation_stats (user_id, period_type, period_date, meditation_duration, energy_gained, tasks_completed) VALUES
(1, 'day', '2025-08-11', 600, 10, 1);

-- 插入坚持记录
INSERT INTO user_streaks (user_id, streak_start, streak_end, total_days) VALUES (1, '2025-08-11', '2025-08-11', 1);

SELECT '简单测试数据插入成功！' as message;
